## 用户密码格式

- 至少 8 位字符
- 至少包含一个字母和一个数字
- 不可以有空格
- 可以有特殊符号

## 用户密码 Hash 操作

目前前端不 Hash 密码，后端接收明文密码后进行 Hash 操作，传输过程依靠 HTTPS 加密通信

后端使用 Argon2id 算法进行密码 Hash 操作，生成随机 Salt 并与密码 Hash 值组合存储

- Memory: 64MB (65536 KB)
- Iterations: 3
- Parallelism: 4

## JWT

本项目使用 JWT（JSON Web Token）进行用户认证和授权。

### 后端生成 Token

当用户使用邮箱和密码成功登录后，后端服务器将生成一个 JWT Token 并返回给客户端。这个 Token 是后续请求的身份凭证。

- **算法 (Algorithm):** `HS256` (HMAC with SHA-256)
- **JWT 结构:** JWT 由三部分组成，通过 `.` 连接：`Header.Payload.Signature`
  - **Header (头部)**

    指定了签名算法和 Token 类型。

    ```json
    {
      "alg": "HS256",
      "typ": "JWT"
    }
    ```

  - **Payload (载荷)**
    - 包含了需要传递的数据（称为 Claims）。为了安全和高效，我们只存放用户的核心身份信息和 Token 的元数据。
    - **标准声明 (Registered Claims):**
      - `sub` (Subject): **用户 ID**。这是 Token 的核心，用于标识该 Token 属于哪个用户。
      - `iat` (Issued At): **签发时间戳**。记录 Token 是何时生成的。
      - `exp` (Expiration Time): **过期时间戳**。这是 Token 的生命周期，**必须设置**以保证安全。本项目设置成 24 小时。
    - **示例 Payload:**

    ```json
    {
      "sub": 123, // 用户的数据库ID
      "email": "<EMAIL>", // 可选，方便调试或某些场景使用
      "iat": 1726651200, // 签发时间 (Unix Timestamp)
      "exp": 1726737600 // 过期时间 (签发时间 + 24小时)
    }
    ```

  - **Signature (签名)**
    - 签名用于验证消息在传递过程中没有被篡改，并且可以验证 Token 的签发者。
    - **生成方式:**
      1.  将 `Header` 和 `Payload` 分别进行 Base64Url 编码。
      2.  将编码后的两部分用 `.` 连接起来。
      3.  使用`HS256`算法和预定义的**密钥 (Secret Key)** 对连接后的字符串进行加密。
    - **密钥 (Secret Key):**
      - 部署的时候用`openssl rand -base64 32`生成一个随机的 32 字节 Base64 编码字符串，放到`.env`文件中作为密钥。运行时用`process.env.JWT_SECRET`来读取

### 后端解析 Token

客户端在收到 Token 后，应将其存储在本地（如 LocalStorage 或 HttpOnly Cookie）。对于需要认证的 API 请求，客户端必须在 HTTP 请求的 `Authorization` Header 中携带 Token。

**格式:** `Authorization: Bearer <jwt_token>`

后端通过一个中间件（Middleware）来保护需要认证的路由。该中间件负责解析和验证 Token。

#### 1. 提取 Token

中间件首先从 `Authorization` Header 中解析出 `<jwt_token>` 部分。如果 Header 不存在或格式不正确，则直接拒绝请求（返回 `401 Unauthorized`）。

#### 2. 验证 Token

后端 JWT 库会使用当初签发时**相同的密钥 (Secret Key)** 来执行以下验证：

1.  **验证签名:** 重新计算 `Header` 和 `Payload` 的签名，并与 Token 中的 `Signature` 部分进行比对。
    - 如果签名不匹配，说明 Token 被篡改或是伪造的，验证失败。
2.  **验证标准声明:**
    - 检查 `exp` 声明，确保 Token 没有过期。如果 `当前时间戳 > exp时间戳`，则验证失败。
    - 可以根据需要检查其他声明（如 `iss` 签发者, `aud` 接收方等，但 MVP 阶段可省略）。

#### 3. 处理结果

**验证成功:**

- Token 是有效且可信的。
- 中间件从 `Payload` 中解析出用户 ID (`sub` 声明)。
- 将用户信息（如用户 ID）附加到请求对象上（例如 `request.user = { id: 123 }`），以便后续的业务逻辑代码可以直接使用。
- 请求被放行，继续执行目标 API 的逻辑。

**验证失败:**

- 无论是签名错误、Token 过期还是格式错误，都意味着请求未被授权。
- 中间件立即中断请求，并向客户端返回 `401 Unauthorized` 错误响应。

---

## 订单业务逻辑

### 订单状态流转规则

订单在系统中有以下四种状态，状态之间的转换遵循严格的业务规则：

- **PENDING_PAYMENT (待付款)**: 订单创建后的初始状态
- **CANCELLED (已取消)**: 用户主动取消或系统超时取消
- **PAID (已付款)**: 用户成功支付后的状态
- **REFUNDED (已退款)**: 用户申请退款并处理完成后的状态

**允许的状态转换:**
- `PENDING_PAYMENT → PAID` (用户支付成功)
- `PENDING_PAYMENT → CANCELLED` (用户取消或系统超时)
- `PAID → REFUNDED` (用户申请退款)

**不允许的状态转换:**
- 已取消的订单不能支付或退款
- 已退款的订单不能再次退款
- 已付款的订单不能直接取消（需要通过退款流程）

### 订单超时机制

为了释放被占用但未付款的座位，系统实施订单超时机制：

- **超时时间**: 订单创建后10分钟内必须完成支付
- **超时处理**: 超过10分钟未支付的订单自动标记为`CANCELLED`状态
- **座位释放**: 订单取消时立即释放占用的座位
- **定时任务**: 系统每10分钟执行一次超时订单清理任务

### 座位库存管理

采用简化的座位管理策略，适合MVP阶段：

- **立即占用**: 订单创建成功时立即占用座位，不等待支付
- **自动分配**: 系统自动分配座位，用户无法选择具体座位号
- **库存计算**: `可用座位数 = 总座位数 - 已付款订单数 - 待付款订单数`
- **并发处理**: MVP阶段采用数据库行锁简单处理，不考虑复杂的高并发场景

### 价格计算规则

MVP阶段采用简化的价格策略：

- **固定价格**: 订单价格直接使用火车时刻表中的票价
- **无优惠**: 暂不考虑折扣、优惠券等复杂价格计算
- **价格一致性**: 订单创建时记录当时的票价，后续票价变动不影响已创建订单

### 退款规则

提供用户友好的退款政策：

- **退款条件**: 只要在火车发车时间之前申请，均可全额退款
- **退款金额**: 100%全额退款，无手续费
- **退款方式**: 退款金额直接返回到购票用户的账户余额
- **退款时效**: 退款申请提交后立即处理，实时到账

### 订单编号生成规则

为了便于用户识别和系统管理，订单编号采用统一格式：

- **格式**: `TK + YYYYMMDD + 8位流水号`
- **示例**: `TK2024091812345678`
- **组成说明**:
  - `TK`: 固定前缀，代表"Train Ticket"
  - `YYYYMMDD`: 订单创建日期
  - `8位流水号`: 当日订单的递增序号，不足8位前补0
- **唯一性保证**: 通过数据库唯一约束确保订单编号不重复
- **流水号重置**: 每日0点重置流水号从00000001开始

### 余额扣款规则

确保资金安全和交易原子性：

- **余额检查**: 支付前必须验证用户余额是否充足
- **余额不足**: 如余额不足则拒绝支付，订单保持`PENDING_PAYMENT`状态
- **扣款原子性**: 余额扣减和订单状态更新在同一数据库事务中完成
- **退款恢复**: 退款时将订单金额原数返回到用户余额账户
- **并发安全**: 使用数据库行锁防止余额并发修改导致的数据不一致

### 批量订单说明

MVP阶段为了简化开发和测试，暂不提供批量创建订单功能。用户如需为多个乘车人购票，需要分别调用单个订单创建API。后续版本将根据用户反馈考虑增加批量购票功能以提升用户体验。
